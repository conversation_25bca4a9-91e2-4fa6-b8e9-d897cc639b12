# UTM Tracking Implementation Guide
## Big River Coffee - Google Analytics 4 Campaign Tracking

### Overview
This implementation provides comprehensive UTM parameter tracking for Big River Coffee's marketing campaigns, integrating seamlessly with Google Analytics 4.

### Features Implemented

#### 1. **UTM Parameter Management** (`app/lib/utm.ts`)
- Automatic UTM parameter extraction from URLs
- Session storage persistence (24-hour expiry)
- UTM parameter validation and formatting
- Pre-configured campaign presets

#### 2. **React Hook for UTM Tracking** (`app/hooks/useUTMTracking.ts`)
- Automatic UTM capture on page load
- Persistent tracking across navigation
- Real-time UTM parameter state management

#### 3. **Enhanced Google Analytics Integration** (`app/components/GoogleAnalytics.tsx`)
- UTM data automatically included in all GA4 events
- Purchase conversion attribution tracking
- Page view tracking with campaign data
- Debug logging for UTM attribution

#### 4. **UTM-Enabled Components** (`app/components/UTMLink.tsx`)
- UTM-aware Link component
- Pre-configured campaign link components
- Support for both internal and external links



### UTM Parameters Supported

| Parameter | Description | Example |
|-----------|-------------|---------|
| `utm_source` | Traffic source | `google`, `facebook`, `newsletter` |
| `utm_medium` | Marketing medium | `cpc`, `email`, `social` |
| `utm_campaign` | Campaign name | `summer_sale`, `product_launch` |
| `utm_content` | Content differentiation | `banner_top`, `cta_button` |
| `utm_term` | Keywords (paid search) | `coffee`, `subscription` |
| `utm_id` | Campaign ID | `12345` |

### Usage Examples

#### 1. **Email Campaign URLs**
```
https://bigrivercoffee.com/collections/all?utm_source=newsletter&utm_medium=email&utm_campaign=monthly_newsletter&utm_content=shop_now_button
```

#### 2. **Social Media Posts**
```
https://bigrivercoffee.com/?utm_source=instagram&utm_medium=social&utm_campaign=coffee_promotion&utm_content=story_link
```

#### 3. **Google Ads**
```
https://bigrivercoffee.com/collections/all?utm_source=google&utm_medium=cpc&utm_campaign=search_coffee&utm_term=nicaraguan_coffee
```

### Implementation Details

#### **Automatic UTM Capture**
- UTM parameters are automatically captured when users visit any page with UTM parameters
- Parameters persist for 24 hours across user navigation
- No manual intervention required

#### **Google Analytics Integration**
- All UTM data is automatically sent to GA4 with every event
- Purchase conversions include full attribution data
- Campaign performance can be tracked in GA4 reports

#### **Component Usage**
```tsx
// Use UTM-enabled links in components
import { UTMLinks } from '~/components/UTMLink';

// Email campaign link
<UTMLinks.EmailCTA to="/collections/all">
  Shop Our Coffee
</UTMLinks.EmailCTA>

// Social media link
<UTMLinks.SocialCTA platform="instagram" to="/products/blend-box">
  Try Our Blend Box
</UTMLinks.SocialCTA>

// Popup/banner link (already implemented)
<UTMLinks.PopupCTA>
  Shop Coffee
</UTMLinks.PopupCTA>
```

### Campaign Tracking in Google Analytics 4

#### **Where to Find UTM Data in GA4:**
1. **Reports > Acquisition > Traffic Acquisition**
   - View by Session source/medium
   - Filter by Campaign name
   - Analyze conversion rates by source

2. **Reports > Acquisition > User Acquisition**
   - First-time user attribution
   - Campaign performance for new customers

3. **Explore > Free Form**
   - Custom reports with UTM dimensions
   - Advanced campaign analysis

#### **Key Metrics to Track:**
- **Sessions by Campaign**: Total traffic from each campaign
- **Conversion Rate**: Purchases per campaign visitor
- **Revenue Attribution**: Total sales attributed to each campaign
- **Customer Acquisition Cost**: Ad spend vs. attributed revenue

### Best Practices

#### **UTM Naming Conventions:**
- **Source**: Use consistent platform names (`google`, `facebook`, `newsletter`)
- **Medium**: Use standard categories (`cpc`, `email`, `social`, `referral`)
- **Campaign**: Use descriptive, unique names (`summer_sale_2024`, `new_product_launch`)
- **Content**: Differentiate creative elements (`banner_top`, `cta_button`, `sidebar_ad`)

#### **Campaign Organization:**
- Use external UTM builder tools or manual URL construction for consistent URL generation
- Document campaign URLs in a spreadsheet for team reference
- Test UTM URLs before launching campaigns
- Monitor GA4 reports regularly for campaign performance

### Testing UTM Implementation

#### **Manual Testing:**
1. Visit: `https://bigrivercoffee.com/?utm_source=test&utm_medium=manual&utm_campaign=testing`
2. Navigate to different pages
3. Check browser console for UTM logging
4. Verify GA4 events include UTM data

#### **Campaign Testing:**
1. Use external UTM builder tools to generate test URLs
2. Test different campaign scenarios
3. Verify attribution in GA4 Real-time reports
4. Test purchase conversion tracking

### Troubleshooting

#### **UTM Parameters Not Showing in GA4:**
- Check browser console for UTM capture logs
- Verify GA4 property ID is correct
- Ensure UTM parameters are properly formatted
- Check GA4 Real-time reports for immediate verification

#### **Attribution Not Working:**
- Verify session storage is enabled in browser
- Check UTM parameter expiry (24 hours)
- Ensure Google Analytics script is loading properly
- Test with different browsers/devices

### Future Enhancements

#### **Potential Additions:**
- Server-side UTM tracking for better reliability
- UTM parameter validation and sanitization
- Advanced attribution modeling
- Integration with email marketing platforms
- Automated campaign performance reporting

---

**For questions or issues with UTM tracking, check the browser console logs or contact the development team.**
